<script setup lang="ts">
import { ref } from 'vue'
import { PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'

// 表单数据
const fileName = ref('政府采购框架协议采购方式管理暂行办法')
const isEnabled = ref(true)
const tags = ref(['标签1', '标签2', '标签3'])
const newTagVisible = ref(false)
const newTagValue = ref('')

// 删除标签
const removeTag = (index: number) => {
  tags.value.splice(index, 1)
}

// 显示新标签输入框
const showNewTagInput = () => {
  newTagVisible.value = true
}

// 添加新标签
const addNewTag = () => {
  if (newTagValue.value && !tags.value.includes(newTagValue.value)) {
    tags.value.push(newTagValue.value)
  }
  newTagVisible.value = false
  newTagValue.value = ''
}


</script>

<template>
  <div class="form-container">
    <div class="form-content">
      <!-- 文件名称 -->
      <div class="form-item">
        <label class="form-label required">文件名称：</label>
        <a-input
          v-model:value="fileName"
          placeholder="请输入文件名称"
          class="form-input"
        />
      </div>

      <!-- 开启状态 -->
      <div class="form-item">
        <label class="form-label required">开启状态：</label>
        <a-switch v-model:checked="isEnabled" />
      </div>

      <!-- 标签 -->
      <div class="form-item">
        <label class="form-label required">标签：</label>
        <div class="tags-container">
          <!-- 现有标签 -->
          <div
            v-for="(tag, index) in tags"
            :key="index"
            class="tag-item"
          >
            <span class="tag-text">{{ tag }}</span>
            <CloseOutlined
              class="tag-close"
              @click="removeTag(index)"
            />
          </div>

          <!-- 新标签输入框 -->
          <div v-if="newTagVisible" class="tag-input-container">
            <a-input
              v-model:value="newTagValue"
              @pressEnter="addNewTag"
              @blur="addNewTag"
              class="tag-input"
              placeholder="输入标签名"
              size="small"
            />
          </div>

          <!-- 新增标签按钮 -->
          <div
            v-else
            class="add-tag-btn"
            @click="showNewTagInput"
          >
            <PlusOutlined class="add-icon" />
            新标签
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.form-container {
  max-width: 800px;
  margin: 40px auto;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.form-label {
  min-width: 100px;
  font-weight: 500;
  color: #262626;
  line-height: 32px;
  flex-shrink: 0;
}

.form-label.required::before {
  content: '*';
  color: #ff4d4f;
  margin-right: 4px;
}

.form-input {
  flex: 1;
  max-width: 400px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #262626;
  cursor: default;
}

.tag-text {
  margin-right: 4px;
}

.tag-close {
  font-size: 12px;
  color: #8c8c8c;
  cursor: pointer;
  transition: color 0.2s;
}

.tag-close:hover {
  color: #ff4d4f;
}

.tag-input-container {
  display: inline-block;
}

.tag-input {
  width: 100px;
}

.add-tag-btn {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  color: #8c8c8c;
  cursor: pointer;
  transition: all 0.2s;
}

.add-tag-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.add-icon {
  margin-right: 4px;
  font-size: 12px;
}
</style>
